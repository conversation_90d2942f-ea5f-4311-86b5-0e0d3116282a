import { createServerClient } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value);
            response.cookies.set(name, value, options);
          });
        },
      },
    }
  );

  // Refresh session if expired - required for Server Components
  await supabase.auth.getUser();

  const {
    data: { session },
  } = await supabase.auth.getSession();

  const nextUrl = request.nextUrl;

  // Allow access to sign-in page and auth callback
  if (nextUrl.pathname === "/sign-in" || nextUrl.pathname === "/auth/callback") {
    return response;
  }

  // Not authenticated - redirect to sign-in page
  if (!session) {
    const encodedSearchParams = `${nextUrl.pathname.substring(1)}${
      nextUrl.search
    }`;

    const url = new URL("/sign-in", request.url);

    if (encodedSearchParams) {
      url.searchParams.append("return_to", encodedSearchParams);
    }

    return NextResponse.redirect(url);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api (API routes)
     * - monitoring (monitoring routes)
     */
    "/((?!_next/static|_next/image|favicon.ico|api|monitoring).*)",
  ],
};
